# 9M83ME - Israel Defense Forces Threat Detection System
# PC Compatible Requirements (Windows/Mac/Linux)

# Core AI/ML Libraries
numpy>=1.21.0
scikit-learn>=1.0.0
joblib>=1.0.0

# Image Processing
Pillow>=8.0.0
opencv-python>=4.5.0

# Deep Learning Frameworks (choose based on your models)
torch>=1.10.0
torchvision>=0.11.0
onnxruntime>=1.10.0
tensorflow>=2.8.0  # Optional, if using TensorFlow models

# Configuration and Data
PyYAML>=6.0
pathlib2>=2.3.0  # For older Python versions

# GUI (Required for standalone app)
PyQt5>=5.15.0
PyQt5-tools>=5.15.0
# Alternative: PyQt6>=6.0.0 (if PyQt5 not available)

# EXE Building
pyinstaller>=5.0.0

# Mobile/Phone Integration
pillow-heif>=0.10.0  # For iPhone HEIC format support

# Utilities
tqdm>=4.60.0  # Progress bars
argparse  # Built-in, but listed for clarity

# Development/Testing (Optional)
pytest>=6.0.0
black>=21.0.0  # Code formatting

# Platform-specific optimizations
# Windows: Add these for better performance
# pywin32>=300  # Windows-specific utilities
# wmi>=1.5.0    # Windows Management Interface

# macOS: Add these for better integration
# pyobjc>=8.0   # macOS integration

# Linux: Usually works out of the box with above packages