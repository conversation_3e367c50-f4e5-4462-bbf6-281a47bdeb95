#!/usr/bin/env python3
"""
Create a simple icon for the Israel Defense Forces application
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_icon():
    """Create a simple icon for the application"""
    print("🎨 Creating application icon...")
    
    # Create assets directory
    os.makedirs("assets", exist_ok=True)
    
    # Create a 256x256 icon
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Israeli flag colors
    blue = (0, 56, 168)  # Israeli blue
    white = (255, 255, 255)
    
    # Draw background circle
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], fill=blue, outline=white, width=8)
    
    # Draw Star of David (simplified)
    center = size // 2
    star_size = 60
    
    # Draw triangles to form Star of David
    points1 = [
        (center, center - star_size),
        (center - star_size * 0.866, center + star_size * 0.5),
        (center + star_size * 0.866, center + star_size * 0.5)
    ]
    
    points2 = [
        (center, center + star_size),
        (center - star_size * 0.866, center - star_size * 0.5),
        (center + star_size * 0.866, center - star_size * 0.5)
    ]
    
    draw.polygon(points1, fill=white, outline=white)
    draw.polygon(points2, fill=white, outline=white)
    
    # Add text "IDF" at bottom
    try:
        # Try to use a system font
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "IDF"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = (size - text_width) // 2
    text_y = size - 50
    
    draw.text((text_x, text_y), text, fill=white, font=font)
    
    # Save as ICO file
    icon_path = "assets/icon.ico"
    
    # Create multiple sizes for ICO file
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    for ico_size in sizes:
        resized = img.resize((ico_size, ico_size), Image.Resampling.LANCZOS)
        images.append(resized)
    
    # Save as ICO
    img.save(icon_path, format='ICO', sizes=[(s, s) for s in sizes])
    
    print(f"✅ Icon created: {icon_path}")
    print("🇮🇱 Israeli flag colors with Star of David")
    
    return icon_path

if __name__ == "__main__":
    create_icon()
