#!/usr/bin/env python3
"""
🇮🇱 Israel Defense Forces - EXE Builder for Visual Studio 2022
Creates standalone Windows executable using PyInstaller
"""

import os
import sys
import subprocess
from pathlib import Path

def build_exe():
    """Build standalone EXE for Windows"""
    print("🇮🇱 Building Israel Defense Forces Threat Detection EXE...")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Check if icon exists, create if needed
    icon_path = "assets/icon.ico"
    if not os.path.exists(icon_path):
        print(f"⚠️  Icon not found: {icon_path}")
        print("🎨 Creating Israeli flag icon...")
        try:
            # Try to create icon
            from create_icon import create_icon
            create_icon()
        except Exception as e:
            print(f"⚠️  Could not create icon: {e}")
            print("📝 Building without custom icon...")
            icon_path = None

    # Build command
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # Single EXE file
        "--windowed",                   # No console window
        "--name=IsraelDefenseForces",   # EXE name
        "--add-data=config;config",     # Include config folder
        "--add-data=models;models",     # Include models folder
        "--add-data=data;data",         # Include data folder
        "--hidden-import=PyQt5",        # Ensure PyQt5 is included
        "--hidden-import=sklearn",      # Ensure scikit-learn is included
        "--hidden-import=torch",        # Ensure PyTorch is included
        "--hidden-import=onnxruntime",  # Ensure ONNX Runtime is included
        "--hidden-import=PIL",          # Ensure Pillow is included
        "--hidden-import=yaml",         # Ensure PyYAML is included
        "--clean",                      # Clean build
        "gui_main.py"                   # Main GUI file
    ]

    # Add icon if it exists
    if icon_path and os.path.exists(icon_path):
        build_cmd.insert(-1, f"--icon={icon_path}")
        print(f"✅ Using icon: {icon_path}")
    else:
        print("ℹ️  Building without custom icon (using default)")
    
    print("🔨 Building EXE with PyInstaller...")
    print(f"Command: {' '.join(build_cmd)}")
    print()
    
    try:
        result = subprocess.run(build_cmd, check=True, capture_output=True, text=True)
        print("✅ Build successful!")
        print()
        print("📁 Output location: dist/IsraelDefenseForces.exe")
        print("📦 File size: ~100-200MB (includes all dependencies)")
        print()
        print("🚀 Ready to deploy on any Windows PC!")
        print("🇮🇱 Protecting Israel from threats! 🇮🇱")
        
    except subprocess.CalledProcessError as e:
        print("❌ Build failed!")
        print(f"Error: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error output: {e.stderr}")
        return False
    
    return True

def create_installer():
    """Create Windows installer using NSIS (optional)"""
    print("\n🎁 Creating Windows Installer...")
    
    nsis_script = """
; Israel Defense Forces Threat Detection Installer
!define APPNAME "Israel Defense Forces - Threat Detection"
!define COMPANYNAME "Israel Defense Forces"
!define DESCRIPTION "AI-powered threat detection system for protecting Israel"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

!include "MUI2.nsh"

Name "${APPNAME}"
OutFile "IsraelDefenseForces_Setup.exe"
InstallDir "$PROGRAMFILES\\${COMPANYNAME}\\${APPNAME}"
RequestExecutionLevel admin

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

!insertmacro MUI_LANGUAGE "English"

Section "Install"
    SetOutPath $INSTDIR
    File "dist\\IsraelDefenseForces.exe"
    File /r "config"
    File /r "models"
    File /r "data"
    
    CreateDirectory "$SMPROGRAMS\\${COMPANYNAME}"
    CreateShortCut "$SMPROGRAMS\\${COMPANYNAME}\\${APPNAME}.lnk" "$INSTDIR\\IsraelDefenseForces.exe"
    CreateShortCut "$DESKTOP\\Israel Defense Forces.lnk" "$INSTDIR\\IsraelDefenseForces.exe"
    
    WriteUninstaller "$INSTDIR\\Uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\IsraelDefenseForces.exe"
    Delete "$INSTDIR\\Uninstall.exe"
    RMDir /r "$INSTDIR"
    Delete "$SMPROGRAMS\\${COMPANYNAME}\\${APPNAME}.lnk"
    Delete "$DESKTOP\\Israel Defense Forces.lnk"
    RMDir "$SMPROGRAMS\\${COMPANYNAME}"
SectionEnd
"""
    
    with open("installer.nsi", "w") as f:
        f.write(nsis_script)
    
    print("📝 NSIS installer script created: installer.nsi")
    print("💡 To build installer: Install NSIS and run 'makensis installer.nsi'")

if __name__ == "__main__":
    success = build_exe()
    if success:
        create_installer()
        print("\n🎯 Build complete! Ready for deployment.")
    else:
        print("\n❌ Build failed. Check errors above.")
        sys.exit(1)
