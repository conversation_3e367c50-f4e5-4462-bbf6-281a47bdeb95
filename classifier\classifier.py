import argparse
import joblib
import numpy as np
from classifier.feature_extractor import extract_features
from config import config_loader

def classify_image(image_path, model=None, return_confidence=False):
    """
    Classify a weapon/aircraft image for REDFOR detection

    Args:
        image_path: Path to the image file
        model: Pre-loaded classifier model (optional)
        return_confidence: Whether to return confidence scores

    Returns:
        If return_confidence=False: predicted class name
        If return_confidence=True: (predicted_class, confidence_score, all_probabilities)
    """
    if model is None:
        config = config_loader.load_config()
        classifier_path = config['paths']['classifier_pkl']
        try:
            model = joblib.load(classifier_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"Classifier model not found at {classifier_path}. Please train a model first.")

    features = extract_features(image_path)
    prediction = model.predict([features])[0]

    if return_confidence:
        # Get prediction probabilities if available
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba([features])[0]
            confidence = np.max(probabilities)

            # Get class names if available
            if hasattr(model, 'classes_'):
                class_probs = dict(zip(model.classes_, probabilities))
            else:
                class_probs = {}

            return prediction, confidence, class_probs
        else:
            return prediction, 1.0, {}

    return prediction

def assess_israel_threat(weapon_range, weapon_class=None):
    """Assess threat to Israeli cities based on weapon range and type"""

    # Extract numeric range
    range_km = 0
    if 'km' in weapon_range:
        try:
            range_km = int(weapon_range.replace('km', '').replace(',', ''))
        except:
            range_km = 0

    threatened_cities = []
    strategic_sites_threatened = []

    # Major Israeli cities and their approximate distances from threat origins
    israeli_cities = {
        'Gaza Border Cities': {'distance': 5, 'cities': ['Sderot', 'Ashkelon', 'Ashdod']},
        'Central Israel': {'distance': 70, 'cities': ['Tel Aviv', 'Ramat Gan', 'Petah Tikva', 'Holon', 'Bnei Brak']},
        'Jerusalem Area': {'distance': 80, 'cities': ['Jerusalem', 'Bethlehem area']},
        'Northern Cities': {'distance': 120, 'cities': ['Haifa', 'Netanya', 'Herzliya']},
        'Southern Cities': {'distance': 150, 'cities': ['Beersheba', 'Dimona']},
        'Far North': {'distance': 200, 'cities': ['Kiryat Shmona', 'Metula']},
        'Eilat': {'distance': 350, 'cities': ['Eilat']}
    }

    # Strategic sites
    strategic_sites = {
        'Ben Gurion Airport': 60,
        'Haifa Port': 120,
        'Ashdod Port': 40,
        'Dimona Nuclear Facility': 150,
        'IDF Bases (Central)': 70
    }

    # Assess which areas are threatened
    for region, data in israeli_cities.items():
        if range_km >= data['distance']:
            threatened_cities.extend(data['cities'])

    for site, distance in strategic_sites.items():
        if range_km >= distance:
            strategic_sites_threatened.append(site)

    # Determine overall threat level
    if range_km >= 350:
        threat_level = "ENTIRE ISRAEL"
    elif range_km >= 200:
        threat_level = "MOST OF ISRAEL"
    elif range_km >= 120:
        threat_level = "CENTRAL & NORTHERN ISRAEL"
    elif range_km >= 70:
        threat_level = "CENTRAL ISRAEL"
    elif range_km >= 40:
        threat_level = "SOUTHERN ISRAEL"
    elif range_km >= 5:
        threat_level = "GAZA BORDER AREA"
    else:
        threat_level = "LIMITED/UNKNOWN"

    return {
        'threat_level': threat_level,
        'threatened_cities': threatened_cities,
        'strategic_sites_threatened': strategic_sites_threatened,
        'range_km': range_km,
        'coverage': f"{len(threatened_cities)} cities threatened"
    }

def get_weapon_info(weapon_class):
    """Get detailed information about detected Iranian/REDFOR weapon"""
    config = config_loader.load_config()
    weapon_classes = config.get('weapon_classes', {})

    # Iranian missile database with ranges and capabilities
    iranian_missile_specs = {
        # Ballistic Missiles
        'Fateh-110': {'range': '300km', 'type': 'Ballistic Missile', 'threat': 'HIGH', 'precision': 'High'},
        'Fateh-313': {'range': '500km', 'type': 'Ballistic Missile', 'threat': 'HIGH', 'precision': 'High'},
        'Zolfaghar': {'range': '700km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Dezful': {'range': '1000km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Qiam-1': {'range': '800km', 'type': 'Ballistic Missile', 'threat': 'HIGH', 'precision': 'Medium'},
        'Shahab-3': {'range': '1300km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'Medium'},
        'Emad': {'range': '1700km', 'type': 'Ballistic Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Khorramshahr': {'range': '2000km', 'type': 'MIRV Ballistic Missile', 'threat': 'EXTREME', 'precision': 'High'},
        'Kheibar-Shekan': {'range': '1450km', 'type': 'Hypersonic Missile', 'threat': 'EXTREME', 'precision': 'High'},
        'Fattah': {'range': '1400km', 'type': 'Hypersonic Glide Vehicle', 'threat': 'EXTREME', 'precision': 'High'},
        'Fattah-2': {'range': '1500km', 'type': 'Advanced Hypersonic', 'threat': 'EXTREME', 'precision': 'High'},

        # Artillery Rockets
        'Fajr-5': {'range': '75km', 'type': 'Artillery Rocket', 'threat': 'HIGH', 'precision': 'Low'},
        'Fajr-3': {'range': '43km', 'type': 'Artillery Rocket', 'threat': 'MEDIUM', 'precision': 'Low'},
        'Zelzal-1': {'range': '125km', 'type': 'Heavy Rocket', 'threat': 'HIGH', 'precision': 'Low'},
        'Zelzal-2': {'range': '210km', 'type': 'Heavy Rocket', 'threat': 'HIGH', 'precision': 'Medium'},

        # Drones/Loitering Munitions
        'Shahed-136': {'range': '2500km', 'type': 'Kamikaze Drone', 'threat': 'HIGH', 'precision': 'High'},
        'Shahed-131': {'range': '900km', 'type': 'Kamikaze Drone', 'threat': 'MEDIUM', 'precision': 'Medium'},
        'Geran-2': {'range': '2500km', 'type': 'Kamikaze Drone (Russian designation)', 'threat': 'HIGH', 'precision': 'High'},

        # Cruise Missiles
        'Hoveyzeh': {'range': '1350km', 'type': 'Cruise Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Paveh': {'range': '1650km', 'type': 'Cruise Missile', 'threat': 'CRITICAL', 'precision': 'High'},
        'Soumar': {'range': '2500km', 'type': 'Cruise Missile', 'threat': 'CRITICAL', 'precision': 'High'},
    }

    # Find weapon category and country
    weapon_country = 'UNKNOWN'
    weapon_category = 'UNKNOWN'

    for category, weapons in weapon_classes.items():
        if weapon_class in weapons:
            weapon_category = category
            if 'iranian' in category.lower():
                weapon_country = 'IRANIAN'
            elif 'russian' in category.lower():
                weapon_country = 'RUSSIAN'
            elif 'chinese' in category.lower():
                weapon_country = 'CHINESE'
            elif 'hezbollah' in category.lower():
                weapon_country = 'HEZBOLLAH/IRANIAN'
            break

    # Get detailed specs if available
    specs = iranian_missile_specs.get(weapon_class, {
        'range': 'Unknown',
        'type': 'Unknown Weapon',
        'threat': 'MEDIUM',
        'precision': 'Unknown'
    })

    return {
        'weapon': weapon_class,
        'country': weapon_country,
        'category': weapon_category,
        'threat_level': specs['threat'],
        'weapon_type': specs['type'],
        'range': specs['range'],
        'precision': specs['precision'],
        'israel_threat': assess_israel_threat(specs['range'], weapon_class)
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("image", help="Path to input image")
    args = parser.parse_args()

    prediction = classify_image(args.image)
    print(f"Predicted class: {prediction}")
