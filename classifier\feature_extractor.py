import os
import numpy as np
import onnxruntime as ort
from PIL import Image
from pathlib import Path
from config import config_loader
import torchvision.transforms as transforms

# Load model path from config
config = config_loader.load_config()
MODEL_PATH = config['paths']['onnx_model']

# Load ONNX model with error handling
def load_onnx_model():
    if not os.path.exists(MODEL_PATH):
        print(f"Warning: ONNX model not found at: {MODEL_PATH}")
        print("Please run with --select-model to choose a valid model")
        return None
    try:
        session = ort.InferenceSession(MODEL_PATH, providers=["CPUExecutionProvider"])
        return session
    except Exception as e:
        print(f"Warning: Failed to load ONNX model: {e}")
        return None

session = load_onnx_model()
if session:
    input_name = session.get_inputs()[0].name
else:
    input_name = None

# Image preprocessing to match ViT-B-16 expectations
transform = transforms.Compose([
    transforms.Resize((240, 240)),  # ViT-B-16-plus-240 expects 240x240
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5]*3, std=[0.5]*3)
])

def extract_features(image_path):
    if session is None:
        raise RuntimeError("ONNX model not loaded. Please select a valid model first.")
    
    try:
        img = Image.open(image_path).convert("RGB")
    except Exception as e:
        raise IOError(f"Failed to open image {image_path}: {e}")
        
    img_tensor = transform(img).unsqueeze(0).numpy()
    
    try:
        outputs = session.run(None, {input_name: img_tensor})
        embedding = outputs[0].squeeze()
        return embedding
    except Exception as e:
        raise RuntimeError(f"Feature extraction failed: {e}")
